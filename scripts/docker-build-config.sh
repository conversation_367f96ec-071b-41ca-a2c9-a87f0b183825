#!/bin/sh

# 获取公司ID，默认为taiyi
COMPANY_ID=${COMPANY_ID:-taiyi}
echo "正在为 ${COMPANY_ID} 公司设置构建环境..."

# 设置基本环境变量
export NODE_ENV=production
export VITE_USE_MOCK=true
export VITE_PUBLIC_PATH=/
export VITE_BUILD_COMPRESS=none
export VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE=false
export VITE_GLOB_API_URL=/jeecg-boot
export VITE_GLOB_DOMAIN_URL=http://localhost:8080/jeecg-boot
export VITE_GLOB_API_URL_PREFIX=

# 根据公司ID设置特定环境变量
case "$COMPANY_ID" in
  "taiyi")
    # 太一公司配置
    export VITE_COMPANY_ID="TaiYi"
    export VITE_COMPANY_NAME="太一企业"
    export VITE_COMPANY_FULL_NAME="福州太一市企业管理有限公司"
    export VITE_SYSTEM_NAME="太一系统"
    export VITE_GLOB_APP_TITLE="太一企业"
    export VITE_COMPANY_LOGO="taiyi_logo.png"
    export VITE_GLOB_APP_SHORT_NAME="TaiYi"
    export VITE_GLOB_COPYRIGHT="©2024 福州太一市企业管理有限公司"
    export VITE_GLOB_ICP="闽ICP备2024066292号-
    ;;
  "xudong")
    # 旭动公司配置
    export VITE_COMPANY_ID="XuDong"
    export VITE_COMPANY_NAME="旭动企业"
    export VITE_COMPANY_FULL_NAME="旭动企业管理有限公司"
    export VITE_SYSTEM_NAME="旭动系统"
    export VITE_GLOB_APP_TITLE="旭动企业"
    export VITE_COMPANY_LOGO="xudong_logo.png"
    export VITE_GLOB_APP_SHORT_NAME="XuDong"
    export VITE_GLOB_COPYRIGHT="©2024 旭动企业管理有限公司"
    export VITE_GLOB_ICP="闽ICP备2024066293号-1"
    ;;
  *)
    echo "错误: 未知的公司ID '${COMPANY_ID}'"
    echo "支持的公司ID: taiyi, xudong"
    exit 1
    ;;
esac

echo "环境变量设置完成，开始构建..."
exec "$@" 