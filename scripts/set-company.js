/**
 * 设置公司信息脚本
 * 用法：node scripts/set-company.js [公司ID]
 * 
 * 例如：node scripts/set-company.js taiyi  - 切换到太一公司配置
 * 或者：node scripts/set-company.js xudong - 切换到旭动公司配置
 */

const fs = require('fs');
const path = require('path');

// 公司配置信息
const companyConfigs = {
  taiyi: {
    id: 'TaiYi',
    logo: 'taiyi_logo.png',
    name: '太一企业',
    shortName: '福州太一',
    fullName: '福州太一市企业管理有限公司',
    systemName: '太一系统',
    copyright: '©2024 福州太一市企业管理有限公司',
    icp: '闽ICP备2024066292号'
  },
  xudong: {
    id: 'XuDong',
    logo: 'xudong_logo.png',
    name: '锦亨润企业',
    shortName: '锦亨润',
    fullName: '山东锦亨润信息科技有限公司',
    systemName: '锦亨润系统',
    copyright: '©2025 山东锦亨润信息科技有限公司',
    icp: '鲁ICP备2025154233号'
  }
};

// 获取命令行参数
const companyId = process.argv[2] || 'taiyi';

if (!companyConfigs[companyId]) {
  console.error(`错误: 未知的公司ID "${companyId}"`);
  console.log('可用的公司ID:');
  Object.keys(companyConfigs).forEach(id => {
    console.log(`- ${id}`);
  });
  process.exit(1);
}

// 读取.env.production文件
const envFile = path.resolve(__dirname, '../.env.production');
let content = '';

try {
  content = fs.readFileSync(envFile, 'utf8');
} catch (error) {
  console.error('读取.env.production文件失败:', error);
  process.exit(1);
}

// 替换配置内容
const config = companyConfigs[companyId];
content = content.replace(/VITE_COMPANY_ID\s*=\s*.*/g, `VITE_COMPANY_ID = ${config.id}`);
content = content.replace(/VITE_COMPANY_LOGO\s*=\s*.*/g, `VITE_COMPANY_LOGO = ${config.logo}`);
content = content.replace(/VITE_COMPANY_NAME\s*=\s*.*/g, `VITE_COMPANY_NAME = ${config.name}`);
content = content.replace(/VITE_COMPANY_FULL_NAME\s*=\s*.*/g, `VITE_COMPANY_FULL_NAME = ${config.fullName}`);
content = content.replace(/VITE_SYSTEM_NAME\s*=\s*.*/g, `VITE_SYSTEM_NAME = ${config.systemName}`);
content = content.replace(/VITE_GLOB_APP_TITLE\s*=\s*.*/g, `VITE_GLOB_APP_TITLE = ${config.name}`);
content = content.replace(/VITE_GLOB_APP_SHORT_NAME\s*=\s*.*/g, `VITE_GLOB_APP_SHORT_NAME = ${config.id}`);
content = content.replace(/VITE_GLOB_COPYRIGHT\s*=\s*.*/g, `VITE_GLOB_COPYRIGHT = ${config.copyright}`);
content = content.replace(/VITE_GLOB_ICP\s*=\s*.*/g, `VITE_GLOB_ICP = ${config.icp}`);

// 更新缓存控制版本
const cacheControlFile = path.resolve(__dirname, '../public/clear-cache.js');
let cacheContent = '';

try {
  cacheContent = fs.readFileSync(cacheControlFile, 'utf8');
  // 更新版本号为当前时间戳
  const newVersion = `const CURRENT_VERSION = "${Date.now()}";`;
  cacheContent = cacheContent.replace(/const CURRENT_VERSION = "[^"]+";/, newVersion);
  fs.writeFileSync(cacheControlFile, cacheContent, 'utf8');
} catch (error) {
  console.error('更新缓存控制版本失败:', error);
}

// 写入文件
try {
  fs.writeFileSync(envFile, content, 'utf8');
  console.log(`✅ 已成功将公司配置切换到: ${config.name}`);
  console.log('\n⚠️  重要提示:');
  console.log('1. 缓存控制已更新，用户访问时会自动清除浏览器缓存');
  console.log('2. 请运行 npm run build 重新构建项目以应用更改');
  console.log('3. 部署后可能需要清除CDN缓存（如果使用了CDN）');

  // 添加编译后钩子，自动创建logo符号链接
  const postBuildScript = path.resolve(__dirname, 'post-build.js');
  const scriptContent = `
/**
 * 构建后处理脚本
 * 自动创建logo符号链接
 */
const fs = require('fs');
const path = require('path');

// 当前使用的公司配置
const currentCompany = '${companyId}';
const logoFile = '${config.logo}';

// 构建完成后创建符号链接
function createLogoSymlink() {
  const imgDir = path.resolve(__dirname, '../dist/resource/img');
  
  if (!fs.existsSync(imgDir)) {
    console.log('❌ 图片目录不存在，跳过创建符号链接');
    return;
  }
  
  const logoPath = path.join(imgDir, 'logo.png');
  const targetPath = path.join(imgDir, logoFile);
  
  try {
    // 删除现有的logo.png（如果存在）
    if (fs.existsSync(logoPath)) {
      fs.unlinkSync(logoPath);
    }
    
    // 创建符号链接
    fs.symlinkSync(logoFile, logoPath);
    console.log(\`✅ 已创建logo符号链接: logo.png -> \${logoFile}\`);
  } catch (error) {
    console.error('创建符号链接失败:', error);
  }
}

// 执行
createLogoSymlink();
`;

  // 写入后构建脚本
  fs.writeFileSync(postBuildScript, scriptContent, 'utf8');
  console.log('4. 已添加后构建脚本，将自动创建logo符号链接');

  // 检查是否需要修改package.json
  const packageJsonFile = path.resolve(__dirname, '../package.json');
  let packageJson = fs.readFileSync(packageJsonFile, 'utf8');
  if (!packageJson.includes('&& node scripts/post-build.js')) {
    packageJson = packageJson.replace(
      /"build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno .\/build\/script\/postBuild.ts"/,
      `"build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts && node scripts/post-build.js"`
    );
    fs.writeFileSync(packageJsonFile, packageJson, 'utf8');
  }
  
} catch (error) {
  console.error('写入.env.production文件失败:', error);
  process.exit(1);
} 