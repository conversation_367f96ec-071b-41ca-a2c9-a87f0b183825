
/**
 * 构建后处理脚本
 * 自动创建logo符号链接
 */
const fs = require('fs');
const path = require('path');

// 当前使用的公司配置
const currentCompany = 'xudong';
const logoFile = 'xudong_logo.png';

// 构建完成后创建符号链接
function createLogoSymlink() {
  const imgDir = path.resolve(__dirname, '../dist/resource/img');
  
  if (!fs.existsSync(imgDir)) {
    console.log('❌ 图片目录不存在，跳过创建符号链接');
    return;
  }
  
  const logoPath = path.join(imgDir, 'logo.png');
  const targetPath = path.join(imgDir, logoFile);
  
  try {
    // 删除现有的logo.png（如果存在）
    if (fs.existsSync(logoPath)) {
      fs.unlinkSync(logoPath);
    }
    
    // 创建符号链接
    fs.symlinkSync(logoFile, logoPath);
    console.log(`✅ 已创建logo符号链接: logo.png -> ${logoFile}`);
  } catch (error) {
    console.error('创建符号链接失败:', error);
  }
}

// 执行
createLogoSymlink();
