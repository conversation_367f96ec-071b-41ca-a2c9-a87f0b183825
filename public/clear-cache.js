/**
 * 缓存控制脚本
 * 用于在更新应用时清除浏览器缓存
 */

// 当前版本号，由构建脚本自动更新
const CURRENT_VERSION = "1743787087216";

// 检查缓存版本
function checkCacheVersion() {
  try {
    const lastVersion = localStorage.getItem('app_version');

    // 如果版本不一致，清除缓存
    if (lastVersion !== CURRENT_VERSION) {
      console.log('检测到版本变更，正在清理缓存...');

      // 保存一些需要保留的值
      const menuStyle = localStorage.getItem("menuStyle");
      const filterField = localStorage.getItem("filterField") || "";
      const plugShopName = localStorage.getItem("plugShopName");

      // 清除本地存储
      localStorage.clear();
      sessionStorage.clear();

      // 恢复需要保留的值
      if (plugShopName) localStorage.setItem("plugShopName", plugShopName);
      localStorage.setItem("menuStyle", menuStyle || "");
      localStorage.setItem("filterField", filterField);

      // 更新版本号
      localStorage.setItem('app_version', CURRENT_VERSION);

      // 如果不是首次加载，刷新页面
      if (lastVersion) {
        console.log('应用已更新，正在刷新...');
        location.reload(true);
      }
    }
  } catch (error) {
    console.error('缓存清理出错:', error);
  }
}

// 立即执行缓存检查，不等待页面加载完成
checkCacheVersion();