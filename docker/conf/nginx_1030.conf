user nginx;
worker_processes  auto;
error_log  /var/log/nginx/error.log;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;
    types_hash_max_size 4096;

    include /etc/nginx/conf.d/*.conf;

    # 配置 for www.taiyibx.com (门户)
    server {
        listen 80;
        server_name www.taiyibx.com;
    
        # 门户页面的路径
        location / {
            alias /usr/share/nginx/html/taiyi/;
            index index-one-page.html;
        }
    }
    
    # 配置 for www.taiyibx.top (Jeecg 管理后台)
    server {
        listen 9000;
        server_name localhost;
    
        # 前端项目的路径
        root /usr/share/nginx/html/taiyihoutai/dist;
        
        location / {
            # 配合 browserHistory 使用
            try_files $uri $uri/ /index.html;
        }
    
        # 后端 jeecgboot 接口转发
        location /jeecgboot/ {
            proxy_pass http://**********:8080/jeecg-boot/;
            proxy_redirect off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}
