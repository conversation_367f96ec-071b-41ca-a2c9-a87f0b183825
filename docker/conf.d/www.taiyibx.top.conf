server {
	listen 9000;
	server_name localhost;

	# 前端项目路径
	root /usr/share/nginx/html/taiyihoutai/dist;

	location / {
		# 配合 browserHistory 使用
		try_files $uri $uri/ /index.html;
	}

	# 后端 jeecgboot 接口转发
	location /jeecg-boot/ {
		proxy_pass http://**********:8080/jeecg-boot/;
		proxy_redirect off;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	}
}
