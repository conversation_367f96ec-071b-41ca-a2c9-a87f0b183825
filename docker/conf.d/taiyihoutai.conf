server {
    listen 80;
    server_name ********;

    # 配置访问 /web 目录
    location /web {
        root /home/<USER>/nginx/html/taiyihoutai/dist;
        index index.html;
    }
    location ^~ /jeecg-boot/ {
    proxy_pass http://**************:8080/jeecg-boot/;
    proxy_redirect off;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    # 可选：如果你需要使用 HTTPS，配置 SSL（可参考你其他的配置）
    # listen 443 ssl;
    # ssl_certificate /path/to/certificate.crt;
    # ssl_certificate_key /path/to/private.key;
}

