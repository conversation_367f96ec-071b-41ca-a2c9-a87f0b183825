server {
    listen 443 ssl;
    server_name www.taiyibx.com;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/www.taiyibx.com_cert_chain.pem;
    ssl_certificate_key /etc/nginx/ssl/www.taiyibx.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:AES128-GCM-SHA256';
    ssl_prefer_server_ciphers on;
    root /usr/share/nginx/html/shouru/dist;
    index index.html;
    # 转发后端接口请求
    location ^~ /jeecg-boot/ {
    proxy_pass http://**********:8080/jeecg-boot/;
    proxy_redirect off;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name www.taiyibx.com;

    # HTTP 请求重定向到 HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}
