server {
    listen 443 ssl;
    server_name www.stormapex.top;

    ssl_certificate /etc/nginx/ssl/stormapex.top_cert_chain.pem;
    ssl_certificate_key /etc/nginx/ssl/stormapex.top.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:AES128-GCM-SHA256';
    ssl_prefer_server_ciphers on;

    # 静态资源根目录
    root /usr/share/nginx/html/taiyihoutai/dist;
    #index index.html;

    # 转发后端接口请求
    location ^~ /jeecg-boot/ {
        proxy_pass http://**************:8080/jeecg-boot/;
        proxy_redirect off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 静态文件处理
    location / {
        try_files $uri $uri/ /index.html;
    }
    location ^~ /product {
        root /usr/share/nginx/html/hweb;
        index index.html;
    }
}

# HTTP 自动重定向到 HTTPS
server {
    listen 80;
    server_name www.stormapex.top;

    location / {
        return 301 https://$host$request_uri;
    }
}
