<template>
  <a-select
    v-bind="attrs"
    :value="selectedValue"
    :options="options"
    @change="handleChange"
    :loading="loading"
    :placeholder="placeholder"
    :showSearch="showSearch"
    :allowClear="allowClear"
    :filterOption="filterOption"
  />
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed, onBeforeMount } from 'vue';
import { propTypes } from '/@/utils/propTypes';
import { loadTenantList, getTenantListFromCache, TENANT_LIST_CACHE_KEY, TENANT_CACHE_DURATION } from '/@/utils/tenant/tenantService';

const props = defineProps({
  value: propTypes.oneOfType([propTypes.string, propTypes.number]),
  placeholder: propTypes.string.def('请选择'),
  showSearch: propTypes.bool.def(true),
  allowClear: propTypes.bool.def(true),
  api: propTypes.string.def('/sys/tenant/getTenantList'),
  params: propTypes.object.def({}),
  labelField: propTypes.string.def('name'),
  valueField: propTypes.string.def('id'),
  cacheKey: propTypes.string.def(TENANT_LIST_CACHE_KEY),
  cacheDuration: propTypes.number.def(TENANT_CACHE_DURATION), // 默认缓存1小时
});

const emit = defineEmits(['change', 'update:value']);
const attrs = computed(() => {
  return {
    ...props,
    value: undefined,
  };
});

const loading = ref(false);
const options = ref<any[]>([]);
const selectedValue = ref(props.value);

// 监听外部传入的value变化
watch(
  () => props.value,
  (val) => {
    selectedValue.value = val;
  }
);

// 过滤选项
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 使用 tenantService 中的方法获取和保存缓存数据

// 获取租户列表
const loadData = async (forceRefresh = false) => {
  try {
    loading.value = true;

    // 使用 tenantService 中的方法加载数据
    const data = await loadTenantList(forceRefresh);

    if (data && data.length > 0) {
      // 确保数据中包含所需的字段
      if (data[0][props.labelField] !== undefined && data[0][props.valueField] !== undefined) {
        options.value = data.map((item) => ({
          label: item[props.labelField],
          value: item[props.valueField],
          ...item,
        }));
        console.log('租户选项数据已加载:', options.value);
      } else {
        console.error('数据中缺少必要的字段:', props.labelField, props.valueField);
        console.log('数据结构:', data[0]);
      }
    } else {
      // 如果没有数据，尝试使用模拟数据进行测试
      console.warn('未获取到租户数据，使用模拟数据');
      const mockData = [
        { id: 0, name: '福州市太一企业管理有限公司' },
        { id: 1002, name: '福州新姚科技有限公司' },
        { id: 1005, name: '保丰' }
      ];
      options.value = mockData.map((item) => ({
        label: item[props.labelField],
        value: item[props.valueField],
        ...item,
      }));
    }
  } catch (error) {
    console.error('获取租户列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理选择变更
const handleChange = (val) => {
  selectedValue.value = val;
  emit('change', val);
  emit('update:value', val);
};

onMounted(() => {
  // 组件挂载时加载数据，使用缓存优先
  loadData(false);
});

// 当参数变化时重新加载数据
watch(
  () => props.params,
  () => {
    loadData(true); // 参数变化时强制刷新
  },
  { deep: true }
);
</script>
