<!--
 * @Author: Vben
 * @Description: Multi-language switching component
-->
<template>
  <Dropdown
    placement="bottom"
    :trigger="['click']"
    :dropMenuList="customLocaleList"
    :selectedKeys="selectedKeys"
    @menuEvent="handleMenuEvent"
    overlayClassName="app-locale-picker-overlay"
  >
    <span class="cursor-pointer flex items-center">
      <Icon icon="ion:language" />
      <span v-if="showText" class="ml-1">{{ getLocaleText }}</span>
    </span>
  </Dropdown>
</template>
<script lang="ts" setup>
  import type { LocaleType } from '/#/config';
  import type { DropMenu } from '/@/components/Dropdown';
  import { ref, watchEffect, unref, computed } from 'vue';
  import { Dropdown } from '/@/components/Dropdown';
  import { Icon } from '/@/components/Icon';
  import { useLocale } from '/@/locales/useLocale';
  import { localeList } from '/@/settings/localeSetting';
  import { defHttp } from '/@/utils/http/axios';
  import { useUserStoreWithOut } from '/@/store/modules/user';
  import { useMessage } from '/@/hooks/web/useMessage';

  const props = defineProps({
    /**
     * Whether to display text
     */
    showText: { type: Boolean, default: true },
    /**
     * Whether to refresh the interface when changing
     */
    reload: { type: Boolean },
    /**
     * 从登录表单传入的用户名
     */
    formUsername: { type: String, default: '' },
  });

  const selectedKeys = ref<string[]>([]);
  const userStore = useUserStoreWithOut();
  const { createMessage } = useMessage();

  const { changeLocale, getLocale } = useLocale();

  // 添加一个新的"繁体中文"选项，专门用于调用接口
  const customLocaleList = computed(() => {
    return [
      ...localeList,
      {
        text: '繁体中文',
        event: 'zh_TW_special',
      },
      {
        text: '日语',
        event: 'ja_special',
      }
    ];
  });

  // 尝试获取登录表单中的用户名（针对登录页面）
  const getFormUsername = (): string => {
    // 首先检查是否有传入的用户名
    if (props.formUsername) {
      return props.formUsername;
    }
    
    try {
      // 尝试从DOM中获取用户名输入框
      const usernameInput = document.querySelector('input[placeholder*="用户名"]') as HTMLInputElement;
      if (usernameInput && usernameInput.value) {
        return usernameInput.value;
      }
      return '';
    } catch (e) {
      console.error('获取登录表单用户名失败', e);
      return '';
    }
  };

  const getLocaleText = computed(() => {
    const key = selectedKeys.value[0];
    if (!key) {
      return '';
    }
    return customLocaleList.value.find((item) => item.event === key)?.text;
  });

  watchEffect(() => {
    selectedKeys.value = [unref(getLocale)];
  });

  async function toggleLocale(lang: LocaleType | string) {
  if (lang === 'zh_TW_special' || lang === 'ja_special') {

    let username = props.formUsername;

    if (!username) {
      const userInfo = userStore.getUserInfo;
      username = userInfo?.username || '';
    }

    if (!username) {
      username = getFormUsername();
    }


    const apiUrl = lang === 'zh_TW_special' ? '/sys/annountCement/reFlash/1' : '/sys/annountCement/reFlash/2';

    try {
      const result = await defHttp.get({
        url: apiUrl,
        params: { username }
      }, { errorMessageMode: 'none' });


      // 删除提示框，只保留日志
      // if (result && (result.success === true || result.code === 200)) {
      //   createMessage.success( `切换${langText}成功`);
      // } else {
      //   createMessage.warning(result.message || `切换${langText}可能未成功`);
      // }

    } catch (error: any) {
      console.error('调用接口失败', error);
      // createMessage.error(`切换${langText}失败: ` + (error.message || '未知错误'));
    }

    return;
  }


    
    // 正常的语言切换流程
    await changeLocale(lang as LocaleType);
    selectedKeys.value = [lang as string];
    
    props.reload && location.reload();
  }

  function handleMenuEvent(menu: DropMenu) {
    if (unref(getLocale) === menu.event && menu.event !== 'zh_TW_special' && menu.event !== 'ja_special') {
      return;
    }
    toggleLocale(menu.event as string);
  }
</script>

<style lang="less">
  .app-locale-picker-overlay {
    .ant-dropdown-menu-item {
      min-width: 160px;
    }
  }
</style>
