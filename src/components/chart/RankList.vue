<template>
  <div class="rank">
    <h4 class="title">{{ title }}</h4>
    <slot name="subtool"></slot>
    <ul class="list" :style="{ height: height ? `${height}px` : 'auto', overflow: 'auto' }">
      <li :key="index" v-for="(item, index) in list" @click="handleClick(item)" :class="activeLine === item.id ? 'active-line' : null">
        <span>{{ item.name }}</span>
      </li>
    </ul>
     <Pagination
        v-model:current="pageNo"
        size="small"
        :total="totalRecord"
        @change="onPageChange"
        style="margin-top: 16px;text-align: right;"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, defineEmits, watch } from 'vue';
  import { Pagination } from 'ant-design-vue';
  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: null,
    },
    height: {
      type: Number,
      default: null,
    },
    totalRecord: {
      type: Number,
      default: 0,
    },
    activeLine: {
      type: String,
      default: '',
    },
    pageSize: {
      type: Number,
      default: 10,
    }
  });
  const pageNo = ref(1);

  const emit = defineEmits(["onPageChange", 'changeRank']);
  async function onPageChange(page) {
    pageNo.value = page;
    emit("onPageChange", page);
  }
  function handleClick(val) {
    emit("changeRank",val);
  }

</script>

<style lang="less" scoped>
  .rank {
    padding: 0 32px 32px 32px;

    .list {
      margin: 25px 0 0;
      padding: 0;
      list-style: none;

      li {
        // margin-top: 16px;
        padding: 8px;
        line-height: 24px;
        border-radius: 4px;
        display: flex;
        cursor: pointer;
        &:hover {
          background-color: #f5f5f5;
        }
        &.active-line {
          background-color: #f5f5f5;
          color: #1890ff;
        }
        span {
          font-size: 14px;
          display: inline-block;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .mobile .rank {
    padding: 0 32px 32px 32px;
  }
</style>
